Stack trace:
Frame         Function      Args
0007FFFFB760  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB760, 0007FFFFA660) msys-2.0.dll+0x1FE8E
0007FFFFB760  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA38) msys-2.0.dll+0x67F9
0007FFFFB760  000210046832 (000210286019, 0007FFFFB618, 0007FFFFB760, 000000000000) msys-2.0.dll+0x6832
0007FFFFB760  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB760  000210068E24 (0007FFFFB770, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA40  00021006A225 (0007FFFFB770, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFF5860000 ntdll.dll
7FFFF3B90000 KERNEL32.DLL
7FFFF2CF0000 KERNELBASE.dll
7FFFEC4B0000 apphelp.dll
7FFFF36B0000 USER32.dll
7FFFF34A0000 win32u.dll
7FFFF45B0000 GDI32.dll
7FFFF3590000 gdi32full.dll
7FFFF3080000 msvcp_win.dll
7FFFF3210000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFF4480000 advapi32.dll
7FFFF42B0000 msvcrt.dll
7FFFF4850000 sechost.dll
7FFFF4170000 RPCRT4.dll
7FFFF24F0000 CRYPTBASE.DLL
7FFFF3190000 bcryptPrimitives.dll
7FFFF45E0000 IMM32.DLL
